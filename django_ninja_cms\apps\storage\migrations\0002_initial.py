# Generated by Django 5.2.4 on 2025-07-12 10:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("storage", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="fileshare",
            name="shared_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shared_files",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="fileshare",
            name="shared_with",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="received_files",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="fileupload",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="uploaded_files",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="fileshare",
            name="file",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="shares",
                to="storage.fileupload",
            ),
        ),
        migrations.AddIndex(
            model_name="fileupload",
            index=models.Index(
                fields=["user", "created_at"], name="storage_fil_user_id_8c74aa_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileupload",
            index=models.Index(
                fields=["file_type"], name="storage_fil_file_ty_8e59f3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileupload",
            index=models.Index(
                fields=["is_public"], name="storage_fil_is_publ_d33616_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileupload",
            index=models.Index(
                fields=["expires_at"], name="storage_fil_expires_3a46ba_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileshare",
            index=models.Index(
                fields=["share_token"], name="storage_fil_share_t_11310b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileshare",
            index=models.Index(
                fields=["shared_by"], name="storage_fil_shared__9d2390_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileshare",
            index=models.Index(
                fields=["shared_with"], name="storage_fil_shared__275264_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileshare",
            index=models.Index(
                fields=["expires_at"], name="storage_fil_expires_eccfb3_idx"
            ),
        ),
    ]
