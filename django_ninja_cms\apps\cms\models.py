"""
Cms 应用数据模型
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel

User = get_user_model()


class Article(BaseModel):
    """文章 模型"""

    title = models.CharField(max_length=255, verbose_name=_("Title"))
    slug = models.CharField(max_length=255, verbose_name=_("Slug"))
    content = models.TextField(verbose_name=_("Content"))
    summary = models.CharField(max_length=255, verbose_name=_("Summary"))
    status = models.CharField(max_length=255, verbose_name=_("Status"))
    is_featured = models.BooleanField(default=False, verbose_name=_("Is Featured"))
    view_count = models.IntegerField(verbose_name=_("View Count"))
    seo_title = models.CharField(max_length=255, verbose_name=_("Seo Title"))
    seo_description = models.TextField(verbose_name=_("Seo Description"))
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_("Author"),
        related_name="authored_articles",
    )
    category = models.ForeignKey(
        "Category",
        on_delete=models.CASCADE,
        verbose_name=_("Category"),
        null=True,
        blank=True,
    )
    tags = models.ManyToManyField("Tag", blank=True, verbose_name=_("Tags"))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="article_set",
        verbose_name=_("所有者"),
    )

    class Meta:
        verbose_name = _("文章")
        verbose_name_plural = _("文章 列表")
        db_table = "cms_article"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return self.name if hasattr(self, "name") else f"Article #{self.id}"


class Category(BaseModel):
    """分类 模型"""

    name = models.CharField(max_length=255, verbose_name=_("Name"))
    slug = models.CharField(max_length=255, verbose_name=_("Slug"))
    description = models.TextField(verbose_name=_("Description"))
    parent = models.ForeignKey(
        "Category",
        on_delete=models.CASCADE,
        verbose_name=_("Parent"),
        null=True,
        blank=True,
    )
    is_active = models.BooleanField(default=False, verbose_name=_("Is Active"))
    sort_order = models.IntegerField(verbose_name=_("Sort Order"))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="category_set",
        verbose_name=_("所有者"),
    )

    class Meta:
        verbose_name = _("分类")
        verbose_name_plural = _("分类 列表")
        db_table = "cms_category"
        ordering = ["sort_order", "name"]
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return self.name if hasattr(self, "name") else f"Category #{self.id}"


class Tag(BaseModel):
    """标签 模型"""

    name = models.CharField(max_length=255, verbose_name=_("Name"))
    slug = models.CharField(max_length=255, verbose_name=_("Slug"))
    color = models.CharField(max_length=255, verbose_name=_("Color"))
    is_active = models.BooleanField(default=False, verbose_name=_("Is Active"))

    owner = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="tag_set", verbose_name=_("所有者")
    )

    class Meta:
        verbose_name = _("标签")
        verbose_name_plural = _("标签 列表")
        db_table = "cms_tag"
        ordering = ["name"]
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return self.name if hasattr(self, "name") else f"Tag #{self.id}"


class Comment(BaseModel):
    """评论 模型"""

    content = models.TextField(verbose_name=_("Content"))
    author = models.ForeignKey(
        "User", on_delete=models.CASCADE, verbose_name=_("Author")
    )
    article = models.ForeignKey(
        "Article", on_delete=models.CASCADE, verbose_name=_("Article")
    )
    parent = models.ForeignKey(
        "Comment", on_delete=models.CASCADE, verbose_name=_("Parent")
    )
    is_approved = models.BooleanField(default=False, verbose_name=_("Is Approved"))
    ip_address = models.CharField(max_length=255, verbose_name=_("Ip Address"))

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="comment_set",
        verbose_name=_("所有者"),
    )

    class Meta:
        verbose_name = _("评论")
        verbose_name_plural = _("评论 列表")
        db_table = "cms_comment"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return self.name if hasattr(self, "name") else f"Comment #{self.id}"


class Media(BaseModel):
    """媒体文件 模型"""

    title = models.CharField(max_length=255, verbose_name=_("Title"))
    file = models.FileField(upload_to="uploads/", verbose_name=_("File"))
    file_type = models.CharField(max_length=255, verbose_name=_("File Type"))
    file_size = models.IntegerField(verbose_name=_("File Size"))
    alt_text = models.CharField(max_length=255, verbose_name=_("Alt Text"))
    uploaded_by = models.ForeignKey(
        "User", on_delete=models.CASCADE, verbose_name=_("Uploaded By")
    )

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="media_set",
        verbose_name=_("所有者"),
    )

    class Meta:
        verbose_name = _("媒体文件")
        verbose_name_plural = _("媒体文件 列表")
        db_table = "cms_media"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["owner"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return self.name if hasattr(self, "name") else f"Media #{self.id}"
