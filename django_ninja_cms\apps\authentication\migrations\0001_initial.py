# Generated by Django 5.2.4 on 2025-07-12 10:28

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "token",
                    models.CharField(max_length=255, unique=True, verbose_name="token"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                ("is_used", models.BooleanField(default=False, verbose_name="is used")),
            ],
            options={
                "verbose_name": "Email Verification Token",
                "verbose_name_plural": "Email Verification Tokens",
                "db_table": "auth_email_verification_tokens",
            },
        ),
        migrations.CreateModel(
            name="LoginAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254, verbose_name="email")),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP address")),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
                ("success", models.BooleanField(verbose_name="success")),
                (
                    "failure_reason",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("invalid_credentials", "Invalid credentials"),
                            ("account_disabled", "Account disabled"),
                            ("account_locked", "Account locked"),
                            ("too_many_attempts", "Too many attempts"),
                        ],
                        max_length=100,
                        verbose_name="failure reason",
                    ),
                ),
                (
                    "attempted_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="attempted at"
                    ),
                ),
            ],
            options={
                "verbose_name": "Login Attempt",
                "verbose_name_plural": "Login Attempts",
                "db_table": "auth_login_attempts",
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "token",
                    models.CharField(max_length=255, unique=True, verbose_name="token"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                ("is_used", models.BooleanField(default=False, verbose_name="is used")),
            ],
            options={
                "verbose_name": "Password Reset Token",
                "verbose_name_plural": "Password Reset Tokens",
                "db_table": "auth_password_reset_tokens",
            },
        ),
        migrations.CreateModel(
            name="RefreshToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.TextField(verbose_name="refresh token")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                (
                    "is_revoked",
                    models.BooleanField(default=False, verbose_name="is revoked"),
                ),
                (
                    "device_name",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="device name"
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP address"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="user agent")),
            ],
            options={
                "verbose_name": "Refresh Token",
                "verbose_name_plural": "Refresh Tokens",
                "db_table": "auth_refresh_tokens",
            },
        ),
    ]
