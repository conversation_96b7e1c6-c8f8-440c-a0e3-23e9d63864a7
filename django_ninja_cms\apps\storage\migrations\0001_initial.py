# Generated by Django 5.2.4 on 2025-07-12 10:28

import apps.storage.models
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="FileShare",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "permission",
                    models.CharField(
                        choices=[
                            ("view", "View Only"),
                            ("download", "Download"),
                            ("edit", "Edit"),
                        ],
                        max_length=20,
                        verbose_name="permission",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(default=False, verbose_name="is public"),
                ),
                (
                    "share_token",
                    models.CharField(
                        max_length=64, unique=True, verbose_name="share token"
                    ),
                ),
                (
                    "password_protected",
                    models.<PERSON><PERSON>anField(
                        default=False, verbose_name="password protected"
                    ),
                ),
                (
                    "password_hash",
                    models.CharField(
                        blank=True, max_length=128, verbose_name="password hash"
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="expires at"
                    ),
                ),
                (
                    "max_downloads",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="max downloads"
                    ),
                ),
                (
                    "download_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="download count"
                    ),
                ),
                (
                    "last_accessed",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last accessed"
                    ),
                ),
            ],
            options={
                "verbose_name": "File Share",
                "verbose_name_plural": "File Shares",
                "db_table": "storage_file_shares",
            },
        ),
        migrations.CreateModel(
            name="FileUpload",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "original_name",
                    models.CharField(max_length=255, verbose_name="original filename"),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to=apps.storage.models.upload_to_path,
                        verbose_name="file",
                    ),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(verbose_name="file size (bytes)"),
                ),
                (
                    "file_type",
                    models.CharField(
                        choices=[
                            ("image", "Image"),
                            ("document", "Document"),
                            ("video", "Video"),
                            ("audio", "Audio"),
                            ("archive", "Archive"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                        verbose_name="file type",
                    ),
                ),
                (
                    "mime_type",
                    models.CharField(max_length=100, verbose_name="MIME type"),
                ),
                (
                    "storage_backend",
                    models.CharField(
                        choices=[
                            ("local", "Local Storage"),
                            ("s3", "Amazon S3"),
                            ("gcs", "Google Cloud Storage"),
                            ("azure", "Azure Blob Storage"),
                        ],
                        default="local",
                        max_length=20,
                        verbose_name="storage backend",
                    ),
                ),
                (
                    "storage_path",
                    models.CharField(max_length=500, verbose_name="storage path"),
                ),
                (
                    "metadata",
                    models.JSONField(blank=True, default=dict, verbose_name="metadata"),
                ),
                (
                    "is_public",
                    models.BooleanField(default=False, verbose_name="is public"),
                ),
                (
                    "is_temporary",
                    models.BooleanField(default=False, verbose_name="is temporary"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="expires at"
                    ),
                ),
                (
                    "width",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="width"
                    ),
                ),
                (
                    "height",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="height"
                    ),
                ),
                (
                    "download_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="download count"
                    ),
                ),
                (
                    "last_accessed",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last accessed"
                    ),
                ),
            ],
            options={
                "verbose_name": "File Upload",
                "verbose_name_plural": "File Uploads",
                "db_table": "storage_file_uploads",
            },
        ),
    ]
